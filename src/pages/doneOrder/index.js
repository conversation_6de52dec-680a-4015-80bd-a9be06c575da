import { Component } from "react";
import { Table } from "antd";
import OrderDetailDrawer from "@/components/OrderDetailDrawer";
import { requestMyDoneOrder } from "@/request/api";
import RoleTag from "@/components/RoleTag";
import styled from "styled-components";

import { OrderPagination } from "@/common/styleLayout";

// css-js start ↓↓↓
const DoneTable = styled(Table)`
  margin-top: 2%;
`
// css-js end   ↑↑↑

export default class DoneOrder extends Component {
  state = {
    dataSource: [],
    pageSize: 12,
    page: 1,
    total: 0,
    filter_by_role: [], // 为迭代三预留角色筛选参数
  };

  // 获取表格列配置
  getColumns = () => {
    return [
      {
        title: "单号",
        dataIndex: "orderID",
        key: "orderID",
        fixed: "left",
        align: "center",
        render: (text, record) => (
          <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <RoleTag roleType={record.role_type} />
            <span>{text}</span>
          </div>
        ),
        // 迭代三：激活角色筛选功能
        filters: [
          { text: '[我申请的]', value: 'APPLICANT' },
          { text: '[抄送我的]', value: 'CC_TO_ME' },
        ],
        filteredValue: this.state.filter_by_role.length > 0 ? this.state.filter_by_role : null,
        filterMultiple: true, // 支持多选
      },
      {
        title: "工单类型",
        dataIndex: "orderTypeName",
        key: "orderTypeName",
        align: "center",
      },
      {
        title: "总节点数",
        dataIndex: "totalStageNum",
        key: "totalStageNum",
        align: "center",
      },
      {
        title: "当前节点",
        dataIndex: "currentStage",
        key: "currentStage",
        align: "center",
      },
      {
        title: "工单状态",
        dataIndex: "resultDesc",
        key: "resultDesc",
        align: "center",
      },
      {
        title: "申请人",
        dataIndex: "propose",
        key: "propose",
        align: "center",
      },
      {
        title: "运维负责人",
        dataIndex: "opsOwner",
        key: "opsOwner",
        align: "center",
      },
      {
        title: "申请日期",
        dataIndex: "ctime",
        key: "ctime",
        align: "center",
        sorter: (a, b) => a.age - b.age,
      },
      {
        title: "详情",
        key: "detail",
        dataIndex: "detail",
        fixed: "right",
        align: "center",
        render: (text, record, index) => {
          return <OrderDetailDrawer title={"详情"} orderID={record.orderID} orderType={record.orderType} />;
        },
      },
    ];
  };
  componentDidMount() {
    this.requestMyDonePageOrder()
  }

  requestMyDonePageOrder = () => {
    requestMyDoneOrder({
      page: this.state.page,
      page_size: this.state.pageSize,
      filter_by_role: this.state.filter_by_role // 传递角色筛选参数
    }).then((data) => {
      var orders = data.orders.map((item, index) => {
        return {
          key: index,
          orderID: item.order_id,
          orderType:item.order_type,
          orderTypeName: item.order_type_name,
          totalStageNum: item.total_stage_num,
          currentStage: item.current_stage,
          resultDesc: item.result_desc,
          propose: item.proposer_email,
          opsOwner: item.ops_owner_email,
          ctime: item.apply_datetime,
          trace: "action",
          role_type: item.role_type, // 添加角色类型字段
        };
      });
      this.setState({ dataSource: orders, total: data.total });
    });
  }

  changePage = (page, pageSize) => {
    this.setState({ page: page }, this.requestMyDonePageOrder)
  }
  changeShowSize = (current, size) => {
    this.setState({ page: 1, pageSize: size }, this.requestMyDonePageOrder)
  }

  // 处理表格变化（分页、排序、筛选）
  handleTableChange = (pagination, filters, sorter) => {
    // 迭代三：实现角色筛选逻辑
    const roleFilters = filters.orderID || [];
    this.setState({
      filter_by_role: roleFilters,
      page: 1 // 筛选时重置到第一页
    }, () => {
      this.requestMyDonePageOrder();
    });
  };

  render() {
    return (
      <div>
        <DoneTable
          className="done-order-table"
          dataSource={this.state.dataSource}
          columns={this.getColumns()}
          size={"middle"}
          pagination={false}
          onChange={this.handleTableChange} // 添加表格变化处理
        />
        <OrderPagination
          size="small"
          showQuickJumper
          showSizeChanger
          defaultCurrent={1}
          total={this.state.total}
          current={this.state.page}
          pageSize={this.state.pageSize}
          pageSizeOptions={[12, 20, 30, 50, 100]}
          onChange={this.changePage}
          onShowSizeChange={this.changeShowSize}
        />
      </div>

    );
  }
}
